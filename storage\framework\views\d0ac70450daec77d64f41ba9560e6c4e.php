<!-- Header starts -->
<header class="user-header">
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
    <div class="container">
      <!-- Logo -->
      <a class="navbar-brand" href="<?php echo e(route('home')); ?>">
        <img src="<?php echo e(asset('assets/images/logo/1.png')); ?>" alt="PandaEdu" height="40">
      </a>

      <!-- Mobile Toggle -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Navigation Menu -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>" href="<?php echo e(route('home')); ?>">
              <i class="fa fa-home me-1"></i>Trang Chủ
            </a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="coursesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-graduation-cap me-1"></i>Khóa Học
            </a>
            <ul class="dropdown-menu" aria-labelledby="coursesDropdown">
              <li><a class="dropdown-item" href="<?php echo e(route('courses.index')); ?>">Tất Cả Khóa Học</a></li>
              <li><hr class="dropdown-divider"></li>
              <?php $__currentLoopData = \App\Models\Category::whereNull('parent_id')->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><a class="dropdown-item" href="<?php echo e(route('courses.category', $category->slug)); ?>"><?php echo e($category->name); ?></a></li>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="<?php echo e(route('categories.index')); ?>">Xem Tất Cả Danh Mục</a></li>
            </ul>
          </li>
          <?php if(auth()->guard()->check()): ?>
            <li class="nav-item">
              <a class="nav-link <?php echo e(request()->routeIs('user.learning.*') ? 'active' : ''); ?>" href="<?php echo e(route('user.learning.index')); ?>">
                <i class="fa fa-book-open me-1"></i>Học Tập Của Tôi
              </a>
            </li>
          <?php endif; ?>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->routeIs('about') ? 'active' : ''); ?>" href="<?php echo e(route('about')); ?>">
              <i class="fa fa-info-circle me-1"></i>Giới Thiệu
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link <?php echo e(request()->routeIs('contact') ? 'active' : ''); ?>" href="<?php echo e(route('contact')); ?>">
              <i class="fa fa-envelope me-1"></i>Liên Hệ
            </a>
          </li>
        </ul>

        <!-- Search Form -->
        <form class="d-flex me-3" action="<?php echo e(route('courses.search')); ?>" method="GET">
          <div class="input-group">
            <input class="form-control" type="search" name="q" placeholder="Tìm Kiếm Khóa Học..." value="<?php echo e(request('q')); ?>">
            <button class="btn btn-outline-primary" type="submit">
              <i class="fa fa-search"></i>
            </button>
          </div>
        </form>

        <!-- User Actions -->
        <ul class="navbar-nav">
          <?php if(auth()->guard()->guest()): ?>
            <li class="nav-item">
              <a class="nav-link" href="<?php echo e(route('login')); ?>">
                <i class="fa fa-sign-in-alt me-1"></i>Đăng Nhập
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link btn btn-primary text-white ms-2" href="<?php echo e(route('register')); ?>">
                <i class="fa fa-user-plus me-1"></i>Đăng Ký
              </a>
            </li>
          <?php else: ?>
            <!-- Wishlist -->
            <li class="nav-item">
              <a class="nav-link position-relative" href="<?php echo e(route('user.wishlist.index')); ?>">
                <i class="fa fa-heart"></i>
                <span class="badge bg-danger position-absolute top-0 start-100 translate-middle">
                  <?php echo e(auth()->user()->wishlists()->count()); ?>

                </span>
              </a>
            </li>

            <!-- Cart -->
            <li class="nav-item">
              <a class="nav-link position-relative" href="<?php echo e(route('user.cart.index')); ?>">
                <i class="fa fa-shopping-cart"></i>
                <span class="badge bg-primary position-absolute top-0 start-100 translate-middle">
                  <?php echo e(session('cart') ? count(session('cart')) : 0); ?>

                </span>
              </a>
            </li>

            <!-- Notifications -->
            <li class="nav-item dropdown">
              <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fa fa-bell"></i>
                <span class="badge bg-danger position-absolute top-0 start-100 translate-middle">
                  <?php echo e(auth()->user()->unreadNotifications->count()); ?>

                </span>
              </a>
              <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationsDropdown">
                <li class="dropdown-header">
                  <h6>Thông Báo</h6>
                  <a href="<?php echo e(route('user.notifications.mark-all-read')); ?>" class="mark-all-read">Đánh Dấu Đã Đọc</a>
                </li>
                <?php $__empty_1 = true; $__currentLoopData = auth()->user()->unreadNotifications->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                  <li>
                    <a class="dropdown-item" href="<?php echo e(route('user.notifications.show', $notification->id)); ?>">
                      <div class="notification-content">
                        <h6><?php echo e($notification->data['title'] ?? 'Thông Báo Mới'); ?></h6>
                        <p><?php echo e(Str::limit($notification->data['message'] ?? '', 50)); ?></p>
                        <small><?php echo e($notification->created_at->diffForHumans()); ?></small>
                      </div>
                    </a>
                  </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                  <li><span class="dropdown-item-text">Không Có Thông Báo Mới</span></li>
                <?php endif; ?>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-center" href="<?php echo e(route('user.notifications.index')); ?>">Xem Tất Cả</a></li>
              </ul>
            </li>

            <!-- User Profile -->
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <img src="<?php echo e(auth()->user()->avatar ?? asset('assets/images/avatar/default.png')); ?>" alt="<?php echo e(auth()->user()->name); ?>" class="rounded-circle me-1" width="30" height="30">
                <?php echo e(auth()->user()->name); ?>

              </a>
              <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                <li><a class="dropdown-item" href="<?php echo e(route('user.dashboard')); ?>">
                  <i class="fa fa-tachometer-alt me-2"></i>Dashboard
                </a></li>
                <li><a class="dropdown-item" href="<?php echo e(route('user.profile.edit')); ?>">
                  <i class="fa fa-user me-2"></i>Thông Tin Cá Nhân
                </a></li>
                <li><a class="dropdown-item" href="<?php echo e(route('user.learning.index')); ?>">
                  <i class="fa fa-book-open me-2"></i>Khóa Học Của Tôi
                </a></li>
                <li><a class="dropdown-item" href="<?php echo e(route('user.orders.index')); ?>">
                  <i class="fa fa-shopping-bag me-2"></i>Lịch Sử Mua Hàng
                </a></li>
                <li><a class="dropdown-item" href="<?php echo e(route('user.notes.index')); ?>">
                  <i class="fa fa-sticky-note me-2"></i>Ghi Chú Của Tôi
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="<?php echo e(route('user.settings')); ?>">
                  <i class="fa fa-cog me-2"></i>Cài Đặt
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <form action="<?php echo e(route('logout')); ?>" method="POST" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="dropdown-item text-danger">
                      <i class="fa fa-sign-out-alt me-2"></i>Đăng Xuất
                    </button>
                  </form>
                </li>
              </ul>
            </li>
          <?php endif; ?>
        </ul>
      </div>
    </div>
  </nav>
</header>
<!-- Header ends -->
<?php /**PATH D:\Src Công Việc\resources\views/layouts/partials/user/header.blade.php ENDPATH**/ ?>