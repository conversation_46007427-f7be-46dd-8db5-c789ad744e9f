{"name": "illuminate/database", "description": "The Illuminate Database package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "keywords": ["laravel", "database", "sql", "orm"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-pdo": "*", "brick/math": "^0.9.3|^0.10.2|^0.11|^0.12", "illuminate/collections": "^10.0", "illuminate/container": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "illuminate/support": "^10.0"}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "conflict": {"carbonphp/carbon-doctrine-types": ">=3.0", "doctrine/dbal": ">=4.0"}, "suggest": {"ext-filter": "Required to use the Postgres database driver.", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^3.5.1).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.21).", "illuminate/console": "Required to use the database commands (^10.0).", "illuminate/events": "Required to use the observers with Eloquent (^10.0).", "illuminate/filesystem": "Required to use the migrations (^10.0).", "illuminate/pagination": "Required to paginate the result set (^10.0).", "symfony/finder": "Required to use Eloquent model factories (^6.2)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}