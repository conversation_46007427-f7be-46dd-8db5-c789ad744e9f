<!-- Footer starts -->
<footer class="user-footer bg-dark text-light py-5">
  <div class="container">
    <div class="row">
      <!-- Company Info -->
      <div class="col-lg-4 col-md-6 mb-4">
        <div class="footer-widget">
          <img src="<?php echo e(asset('assets/images/logo/1.png')); ?>" alt="PandaEdu" class="mb-3" height="40">
          <p class="mb-3">
            PandaEdu là nền tảng học trực tuyến hàng đầu Việt Nam, cung cấp các khóa học chất lượng cao với phương pháp giảng dạy hiện đại và tương tác.
          </p>
          <div class="social-links">
            <a href="#" class="text-light me-3"><i class="fa fa-facebook-f"></i></a>
            <a href="#" class="text-light me-3"><i class="fa fa-twitter"></i></a>
            <a href="#" class="text-light me-3"><i class="fa fa-instagram"></i></a>
            <a href="#" class="text-light me-3"><i class="fa fa-youtube"></i></a>
            <a href="#" class="text-light"><i class="fa fa-linkedin"></i></a>
          </div>
        </div>
      </div>

      <!-- Quick Links -->
      <div class="col-lg-2 col-md-6 mb-4">
        <div class="footer-widget">
          <h5 class="mb-3">Liên Kết Nhanh</h5>
          <ul class="list-unstyled">
            <li><a href="<?php echo e(route('home')); ?>" class="text-light text-decoration-none">Trang Chủ</a></li>
            <li><a href="<?php echo e(route('courses.index')); ?>" class="text-light text-decoration-none">Khóa Học</a></li>
            <li><a href="<?php echo e(route('categories.index')); ?>" class="text-light text-decoration-none">Danh Mục</a></li>
            <li><a href="<?php echo e(route('about')); ?>" class="text-light text-decoration-none">Giới Thiệu</a></li>
            <li><a href="<?php echo e(route('contact')); ?>" class="text-light text-decoration-none">Liên Hệ</a></li>
          </ul>
        </div>
      </div>

      <!-- Categories -->
      <div class="col-lg-2 col-md-6 mb-4">
        <div class="footer-widget">
          <h5 class="mb-3">Danh Mục Phổ Biến</h5>
          <ul class="list-unstyled">
            <?php $__currentLoopData = \App\Models\Category::whereNull('parent_id')->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <li><a href="<?php echo e(route('courses.category', $category->slug)); ?>" class="text-light text-decoration-none"><?php echo e($category->name); ?></a></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </ul>
        </div>
      </div>

      <!-- Support -->
      <div class="col-lg-2 col-md-6 mb-4">
        <div class="footer-widget">
          <h5 class="mb-3">Hỗ Trợ</h5>
          <ul class="list-unstyled">
            <li><a href="<?php echo e(route('help')); ?>" class="text-light text-decoration-none">Trợ Giúp</a></li>
            <li><a href="<?php echo e(route('faq')); ?>" class="text-light text-decoration-none">Câu Hỏi Thường Gặp</a></li>
            <li><a href="<?php echo e(route('terms')); ?>" class="text-light text-decoration-none">Điều Khoản Sử Dụng</a></li>
            <li><a href="<?php echo e(route('privacy')); ?>" class="text-light text-decoration-none">Chính Sách Bảo Mật</a></li>
            <li><a href="<?php echo e(route('refund')); ?>" class="text-light text-decoration-none">Chính Sách Hoàn Tiền</a></li>
          </ul>
        </div>
      </div>

      <!-- Contact Info -->
      <div class="col-lg-2 col-md-6 mb-4">
        <div class="footer-widget">
          <h5 class="mb-3">Liên Hệ</h5>
          <ul class="list-unstyled">
            <li class="mb-2">
              <i class="fa fa-map-marker-alt me-2"></i>
              123 Đường ABC, Quận 1, TP.HCM
            </li>
            <li class="mb-2">
              <i class="fa fa-phone me-2"></i>
              <a href="tel:+84123456789" class="text-light text-decoration-none">+84 123 456 789</a>
            </li>
            <li class="mb-2">
              <i class="fa fa-envelope me-2"></i>
              <a href="mailto:<EMAIL>" class="text-light text-decoration-none"><EMAIL></a>
            </li>
            <li>
              <i class="fa fa-clock me-2"></i>
              24/7 Hỗ Trợ Trực Tuyến
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Newsletter -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="newsletter-section bg-primary p-4 rounded">
          <div class="row align-items-center">
            <div class="col-lg-6">
              <h5 class="mb-2">Đăng Ký Nhận Thông Tin Mới Nhất</h5>
              <p class="mb-0">Nhận thông báo về các khóa học mới và ưu đãi đặc biệt</p>
            </div>
            <div class="col-lg-6">
              <form action="<?php echo e(route('newsletter.subscribe')); ?>" method="POST" class="d-flex">
                <?php echo csrf_field(); ?>
                <input type="email" name="email" class="form-control me-2" placeholder="Nhập Email Của Bạn..." required>
                <button type="submit" class="btn btn-light">Đăng Ký</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Copyright -->
    <div class="row mt-4 pt-4 border-top">
      <div class="col-lg-6">
        <p class="mb-0">© <?php echo e(date('Y')); ?> <strong>PandaEdu</strong>. Bản Quyền Thuộc Về PandaEdu Team.</p>
      </div>
      <div class="col-lg-6 text-lg-end">
        <p class="mb-0">
          Được Phát Triển Với <i class="fa fa-heart text-danger"></i> Tại Việt Nam
        </p>
      </div>
    </div>
  </div>
</footer>

<!-- Back to Top Button -->
<button type="button" class="btn btn-primary btn-floating btn-lg" id="btn-back-to-top">
  <i class="fa fa-arrow-up"></i>
</button>

<script>
// Back to top button
let mybutton = document.getElementById("btn-back-to-top");

window.onscroll = function () {
  scrollFunction();
};

function scrollFunction() {
  if (
    document.body.scrollTop > 20 ||
    document.documentElement.scrollTop > 20
  ) {
    mybutton.style.display = "block";
  } else {
    mybutton.style.display = "none";
  }
}

mybutton.addEventListener("click", backToTop);

function backToTop() {
  document.body.scrollTop = 0;
  document.documentElement.scrollTop = 0;
}
</script>

<style>
#btn-back-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: none;
  z-index: 1000;
}
</style>
<!-- Footer ends -->
<?php /**PATH D:\Src Công Việc\resources\views/layouts/partials/user/footer.blade.php ENDPATH**/ ?>