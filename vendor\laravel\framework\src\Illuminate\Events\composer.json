{"name": "illuminate/events", "description": "The Illuminate Events package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "illuminate/bus": "^10.0", "illuminate/collections": "^10.0", "illuminate/container": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "illuminate/support": "^10.0"}, "autoload": {"psr-4": {"Illuminate\\Events\\": ""}, "files": ["functions.php"]}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}