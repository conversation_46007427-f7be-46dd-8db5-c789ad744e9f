@extends('layouts.user')

@section('title', '<PERSON><PERSON><PERSON> - <PERSON>daEdu')
@section('meta_description', '<PERSON><PERSON><PERSON> t<PERSON><PERSON> k<PERSON><PERSON>n <PERSON>du để bắt đầu hành trình học tập')

@push('styles')
<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.auth-logo {
    text-align: center;
    margin-bottom: 30px;
}

.auth-logo h3 {
    color: #667eea;
    font-weight: bold;
    margin-bottom: 10px;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #f0f0f0;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.password-strength {
    margin-top: 5px;
}

.strength-bar {
    height: 4px;
    border-radius: 2px;
    background: #f0f0f0;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    width: 0%;
}

.strength-weak { background: #dc3545; }
.strength-medium { background: #ffc107; }
.strength-strong { background: #28a745; }

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #f0f0f0;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #999;
    font-size: 14px;
}
</style>
@endpush

@section('content')
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="auth-card">
                    <div class="auth-logo">
                        <h3><i class="fa fa-graduation-cap"></i> PandaEdu</h3>
                        <p class="text-muted">Tạo tài khoản để bắt đầu học tập</p>
                    </div>

                    <form method="POST" action="{{ route('register') }}" id="registerForm">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Họ và tên</label>
                            <input type="text" 
                                   class="form-control @error('name') is-invalid @enderror" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}" 
                                   required 
                                   autocomplete="name" 
                                   autofocus
                                   placeholder="Nhập họ và tên của bạn">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}" 
                                   required 
                                   autocomplete="email"
                                   placeholder="Nhập email của bạn">
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Mật khẩu</label>
                            <input type="password" 
                                   class="form-control @error('password') is-invalid @enderror" 
                                   id="password" 
                                   name="password" 
                                   required 
                                   autocomplete="new-password"
                                   placeholder="Nhập mật khẩu (ít nhất 6 ký tự)">
                            <div class="password-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strengthFill"></div>
                                </div>
                                <small class="text-muted" id="strengthText">Độ mạnh mật khẩu</small>
                            </div>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">Xác nhận mật khẩu</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="password_confirmation" 
                                   name="password_confirmation" 
                                   required 
                                   autocomplete="new-password"
                                   placeholder="Nhập lại mật khẩu">
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                Tôi đồng ý với 
                                <a href="{{ route('terms') }}" target="_blank" class="text-decoration-none">
                                    Điều khoản sử dụng
                                </a> 
                                và 
                                <a href="{{ route('privacy') }}" target="_blank" class="text-decoration-none">
                                    Chính sách bảo mật
                                </a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fa fa-user-plus me-2"></i>
                            Đăng Ký
                        </button>
                    </form>

                    <div class="divider">
                        <span>hoặc</span>
                    </div>

                    <div class="text-center">
                        <p class="mb-0">Đã có tài khoản? 
                            <a href="{{ route('login') }}" class="text-decoration-none fw-bold">
                                Đăng nhập ngay
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Password strength checker
    $('#password').on('input', function() {
        const password = $(this).val();
        const strength = checkPasswordStrength(password);
        updatePasswordStrength(strength);
    });
    
    function checkPasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 6) score++;
        if (password.length >= 8) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/[0-9]/.test(password)) score++;
        if (/[^A-Za-z0-9]/.test(password)) score++;
        
        if (score < 3) return 'weak';
        if (score < 5) return 'medium';
        return 'strong';
    }
    
    function updatePasswordStrength(strength) {
        const fill = $('#strengthFill');
        const text = $('#strengthText');
        
        fill.removeClass('strength-weak strength-medium strength-strong');
        
        switch(strength) {
            case 'weak':
                fill.addClass('strength-weak').css('width', '33%');
                text.text('Mật khẩu yếu').css('color', '#dc3545');
                break;
            case 'medium':
                fill.addClass('strength-medium').css('width', '66%');
                text.text('Mật khẩu trung bình').css('color', '#ffc107');
                break;
            case 'strong':
                fill.addClass('strength-strong').css('width', '100%');
                text.text('Mật khẩu mạnh').css('color', '#28a745');
                break;
        }
    }
    
    // Form validation
    $('#registerForm').on('submit', function(e) {
        const password = $('#password').val();
        const confirmPassword = $('#password_confirmation').val();
        
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Mật khẩu xác nhận không khớp!');
            return false;
        }
        
        if (!$('#terms').is(':checked')) {
            e.preventDefault();
            alert('Vui lòng đồng ý với điều khoản sử dụng!');
            return false;
        }
    });
});
</script>
@endpush
