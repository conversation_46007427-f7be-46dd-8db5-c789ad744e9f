<!DOCTYPE html>
<html lang="vi">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<?php echo $__env->yieldContent('meta_description', 'PandaEdu - Nền Tảng Học Trực <PERSON>ến Hàng Đầu Việt Nam'); ?>">
  <meta name="keywords" content="<?php echo $__env->yieldContent('meta_keywords', 'học trực tuy<PERSON>, kh<PERSON><PERSON> h<PERSON>, gi<PERSON><PERSON> d<PERSON>, Panda<PERSON>du, e-learning'); ?>">
  <meta name="author" content="PandaEdu Team">
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  
  <!-- SEO Meta Tags -->
  <meta property="og:title" content="<?php echo $__env->yieldContent('og_title', 'PandaEdu - Nền <PERSON>'); ?>">
  <meta property="og:description" content="<?php echo $__env->yieldContent('og_description', 'Học trực tuyến với PandaEdu - Nền tảng giáo dục hiện đại'); ?>">
  <meta property="og:image" content="<?php echo $__env->yieldContent('og_image', asset('assets/images/og-image.jpg')); ?>">
  <meta property="og:url" content="<?php echo e(url()->current()); ?>">
  <meta property="og:type" content="website">
  
  <link rel="icon" href="<?php echo e(asset('assets/images/logo/favicon.png')); ?>" type="image/x-icon">
  <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo/favicon.png')); ?>" type="image/x-icon">
  
  <title><?php echo $__env->yieldContent('title', 'PandaEdu - Nền Tảng Học Trực Tuyến'); ?></title>

  <!-- Font Awesome CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/fontawesome/css/all.css')); ?>">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Golos+Text:wght@400..900&display=swap" rel="stylesheet">

  <!-- Animation CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/animation/animate.min.css')); ?>">

  <!-- Tabler Icons -->
  <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/tabler-icons/tabler-icons.css')); ?>">

  <!-- Bootstrap CSS -->
  <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/bootstrap/bootstrap.min.css')); ?>">

  <!-- GLightbox CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/glightbox/glightbox.min.css')); ?>">

  <!-- Slick CSS -->
  <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/slick/slick.css')); ?>">
  <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/slick/slick-theme.css')); ?>">

  <!-- App CSS -->
  <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/css/style.css')); ?>">

  <!-- Responsive CSS -->
  <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/css/responsive.css')); ?>">

  <!-- Custom User CSS -->
  <link rel="stylesheet" type="text/css" href="<?php echo e(asset('css/user.css')); ?>">

  <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body class="user-layout">
  <!-- Loader -->
  <div class="loader-wrapper">
    <div class="app-loader">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
  </div>

  <!-- Header -->
  <?php echo $__env->make('layouts.partials.user.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  <!-- Main Content -->
  <main class="main-content">
    <?php if(session('success')): ?>
      <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <ul class="mb-0">
          <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li><?php echo e($error); ?></li>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    <?php endif; ?>

    <?php echo $__env->yieldContent('content'); ?>
  </main>

  <!-- Footer -->
  <?php echo $__env->make('layouts.partials.user.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  <!-- jQuery -->
  <script src="<?php echo e(asset('assets/vendor/jquery/jquery.min.js')); ?>"></script>

  <!-- Bootstrap JS -->
  <script src="<?php echo e(asset('assets/vendor/bootstrap/bootstrap.bundle.min.js')); ?>"></script>

  <!-- GLightbox JS -->
  <script src="<?php echo e(asset('assets/vendor/glightbox/glightbox.min.js')); ?>"></script>

  <!-- Slick JS -->
  <script src="<?php echo e(asset('assets/vendor/slick/slick.min.js')); ?>"></script>

  <!-- App JS -->
  <script src="<?php echo e(asset('assets/js/script.js')); ?>"></script>

  <!-- Custom User JS -->
  <script src="<?php echo e(asset('js/user.js')); ?>"></script>

  <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH D:\Src Công Việc\resources\views/layouts/user.blade.php ENDPATH**/ ?>